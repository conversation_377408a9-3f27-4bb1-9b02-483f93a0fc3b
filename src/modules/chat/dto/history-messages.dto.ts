/* eslint-disable max-classes-per-file */
import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNumber, IsOptional, Min } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * 获取历史消息详情请求 DTO
 */
export class GetHistoryMessagesDto {
  @ApiProperty({
    description: '系统id，标识业务平台',
    example: 'web-assiatant',
  })
  @IsString()
  appId: string;

  @ApiProperty({
    description: '当前系统登录用户id',
    example: 'user123',
  })
  @IsString()
  userid: string;

  @ApiProperty({
    description: '分页页码',
    example: 1,
    minimum: 1,
  })
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  pageNum: number;

  @ApiProperty({
    description: '分页大小',
    example: 6,
    default: 6,
    minimum: 1,
  })
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  pageSize = 6;

  @ApiProperty({
    description: '会话id',
    example: 'conv_123456789',
  })
  @IsString()
  conversationId: string;

  @ApiProperty({
    description: 'hiagent接口涉及，鉴权字段',
    required: false,
    example: 'your-app-key',
  })
  @IsOptional()
  @IsString()
  appKey?: string;
}

/**
 * 消息信息 DTO (TMessage 类型)
 */
export class TMessageDto {
  @ApiProperty({
    description: '消息ID',
    example: 'msg_123456789',
  })
  messageId: string;

  @ApiProperty({
    description: '消息内容',
    example: '这是一条消息内容',
  })
  content: string;

  @ApiProperty({
    description: '消息类型',
    example: 'text',
  })
  type: string;

  @ApiProperty({
    description: '发送者类型',
    example: 'user',
  })
  sender: string;

  @ApiProperty({
    description: '消息创建时间',
    example: '2024-01-15 10:30:00',
  })
  createTime: string;

  // 可以根据实际的 TMessage 类型添加更多字段
}

/**
 * 获取历史消息详情响应 DTO
 */
export class GetHistoryMessagesResponseDto {
  @ApiProperty({
    description: '状态码，0表示成功，非0表示失败',
    example: '0',
  })
  code: string;

  @ApiProperty({
    description: '失败时的错误消息',
    example: '操作成功',
  })
  msg: string;

  @ApiProperty({
    description: '响应数据',
    type: 'object',
    properties: {
      list: {
        type: 'array',
        items: { $ref: '#/components/schemas/TMessageDto' },
        description: '包含 TMessage 类型元素的数组',
      },
      noMore: {
        type: 'boolean',
        description: '当不存在更多消息时，该值为 true',
      },
    },
  })
  resultData: {
    list: TMessageDto[];
    noMore: boolean;
  };
}
