/* eslint-disable max-classes-per-file */
import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsArray, IsOptional, IsIn } from 'class-validator';

/**
 * 反馈原因 DTO
 */
export class FeedbackReasonDto {
  @ApiProperty({
    description: '用户选中的快捷原因标签id列表（点踩原因相关，可选）',
    type: [String],
    required: false,
    example: ['reason1', 'reason2'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  selectedIds?: string[];

  @ApiProperty({
    description: '用户输入的原因（点踩原因相关，可选）',
    required: false,
    example: '回答不够准确',
  })
  @IsOptional()
  @IsString()
  inputReason?: string;
}

/**
 * 点赞/点踩请求 DTO
 */
export class FeedbackDto {
  @ApiProperty({
    description: '系统id，标识不同业务平台',
    example: 'web-assiatant',
  })
  @IsString()
  appId: string;

  @ApiProperty({
    description: '当前系统唯一用户id',
    example: 'user123',
  })
  @IsString()
  userid: string;

  @ApiProperty({
    description: '会话id',
    example: 'conv_123456789',
  })
  @IsString()
  conversationId: string;

  @ApiProperty({
    description: '消息id',
    example: 'msg_123456789',
  })
  @IsString()
  messageId: string;

  @ApiProperty({
    description: '评分，good表示点赞，bad表示点踩',
    enum: ['good', 'bad'],
    example: 'good',
  })
  @IsString()
  @IsIn(['good', 'bad'])
  score: 'good' | 'bad';

  @ApiProperty({
    description: '操作类型，submit表示首次点击，reset表示已点击过再次点击',
    enum: ['submit', 'reset'],
    example: 'submit',
  })
  @IsString()
  @IsIn(['submit', 'reset'])
  type: 'submit' | 'reset';

  @ApiProperty({
    description: '反馈原因（点踩时使用）',
    type: FeedbackReasonDto,
    required: false,
  })
  @IsOptional()
  reason?: FeedbackReasonDto;

  @ApiProperty({
    description: 'hiagent接口涉及，鉴权字段',
    required: false,
    example: 'your-app-key',
  })
  @IsOptional()
  @IsString()
  appKey?: string;
}

/**
 * 点赞/点踩响应 DTO
 */
export class FeedbackResponseDto {
  @ApiProperty({
    description: '状态码，0表示成功，非0表示失败',
    example: '0',
  })
  code: string;

  @ApiProperty({
    description: '失败时的错误消息',
    example: '操作成功',
  })
  msg: string;
}
